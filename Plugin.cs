using MediaBrowser.Common.Configuration;
using MediaBrowser.Common.Plugins;
using MediaBrowser.Model.Drawing;
using System;
using System.IO;

namespace Emby.MeiamSub.WebDav
{

    /// <summary>
    /// 插件入口
    /// </summary>
    public class Plugin : BasePlugin, IHasThumbImage
    {

        public Plugin(IApplicationPaths applicationPaths)
        {
            Instance = this;
        }

        /// <summary>
        /// 插件ID
        /// </summary>
        public override Guid Id => new Guid("B8F9A7E3-2D4C-4F1A-8E6B-9C5D7A1F3E8B");

        /// <summary>
        /// 插件名称
        /// </summary>
        public override string Name => "MeiamSub.WebDav";

        /// <summary>
        /// 插件描述
        /// </summary>
        public override string Description => "Download subtitles from WebDAV server";

        /// <summary>
        /// 缩略图格式化类型
        /// </summary>
        public ImageFormat ThumbImageFormat => ImageFormat.Gif;


        public static Plugin Instance { get; private set; }

        /// <summary>
        /// 缩略图资源文件
        /// </summary>
        /// <returns></returns>
        public Stream GetThumbImage()
        {
            var type = GetType();
            return type.Assembly.GetManifestResourceStream(type.Namespace + ".Thumb.png");
        }
    }
}
