using System;
using System.Net;
using System.Threading.Tasks;
using WebDav;

namespace Emby.MeiamSub.Shooter
{
    public class WebDavTest
    {
        public static async Task TestWebDavConnection()
        {
            try
            {
                var webDavClient = new WebDavClient(new WebDavClientParams
                {
                    BaseAddress = new Uri("https://vktdudkaokuymf.28.al/dav"),
                    Credentials = new NetworkCredential("7D9CC4C4E258C4EB23432D", "dbJZ$DLCXdakRiacSMB")
                });

                Console.WriteLine("Testing WebDAV connection...");

                // Test the Propfind call
                var propFindResult = await webDavClient.Propfind("/");

                if (propFindResult.IsSuccessful)
                {
                    Console.WriteLine("WebDAV connection successful!");
                    Console.WriteLine($"Found {propFindResult.Resources.Count} resources");
                }
                else
                {
                    Console.WriteLine($"WebDAV connection failed with status code: {propFindResult.StatusCode}");
                    Console.WriteLine($"Description: {propFindResult.Description}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"WebDAV test failed with exception: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
