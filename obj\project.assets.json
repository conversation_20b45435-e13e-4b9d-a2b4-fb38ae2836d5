{"version": 3, "targets": {".NETStandard,Version=v2.1": {"MediaBrowser.Common/4.8.10": {"type": "package", "compile": {"lib/netstandard2.0/Emby.Media.Model.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/Emby.Web.GenericEdit.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/MediaBrowser.Common.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/MediaBrowser.Model.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Emby.Media.Model.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/Emby.Web.GenericEdit.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/MediaBrowser.Common.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/MediaBrowser.Model.dll": {"related": ".pdb;.xml"}}}, "MediaBrowser.Server.Core/4.8.10": {"type": "package", "dependencies": {"MediaBrowser.Common": "4.8.10"}, "compile": {"lib/netstandard2.0/Emby.Naming.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/MediaBrowser.Controller.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Emby.Naming.dll": {"related": ".pdb;.xml"}, "lib/netstandard2.0/MediaBrowser.Controller.dll": {"related": ".pdb;.xml"}}}, "WebDav.Client/2.9.0": {"type": "package", "compile": {"lib/netstandard2.0/WebDav.Client.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/WebDav.Client.dll": {"related": ".pdb;.xml"}}}}}, "libraries": {"MediaBrowser.Common/4.8.10": {"sha512": "YsfE4EiUDW614Yypx4654sc2P/3BvyiyKD5g1HbaQVfhXHyS8pCFp5fX8QSZIZCZnpPMxjixNaZosgXibs2q6Q==", "type": "package", "path": "mediabrowser.common/4.8.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Emby.Media.Model.dll", "lib/netstandard2.0/Emby.Media.Model.pdb", "lib/netstandard2.0/Emby.Media.Model.xml", "lib/netstandard2.0/Emby.Web.GenericEdit.dll", "lib/netstandard2.0/Emby.Web.GenericEdit.pdb", "lib/netstandard2.0/Emby.Web.GenericEdit.xml", "lib/netstandard2.0/MediaBrowser.Common.dll", "lib/netstandard2.0/MediaBrowser.Common.pdb", "lib/netstandard2.0/MediaBrowser.Common.xml", "lib/netstandard2.0/MediaBrowser.Model.dll", "lib/netstandard2.0/MediaBrowser.Model.pdb", "lib/netstandard2.0/MediaBrowser.Model.xml", "mediabrowser.common.4.8.10.nupkg.sha512", "mediabrowser.common.nuspec"]}, "MediaBrowser.Server.Core/4.8.10": {"sha512": "I3FeJrOY6GPRbOJ1yAU3bjozATJcpbk9z4OiNRMvdSw6NuaKPUywFnLnxudc4wltysw4zhPCrl1uqc1qBUBvyA==", "type": "package", "path": "mediabrowser.server.core/4.8.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Emby.Naming.dll", "lib/netstandard2.0/Emby.Naming.pdb", "lib/netstandard2.0/Emby.Naming.xml", "lib/netstandard2.0/MediaBrowser.Controller.dll", "lib/netstandard2.0/MediaBrowser.Controller.pdb", "lib/netstandard2.0/MediaBrowser.Controller.xml", "mediabrowser.server.core.4.8.10.nupkg.sha512", "mediabrowser.server.core.nuspec"]}, "WebDav.Client/2.9.0": {"sha512": "GLhd1tQAJeuVO1sj3Wm/dkg0GEVWxk+XGl6rdegMSMHenZuOaWQw4PifWDsjNEC1dtV1/C8JJfK0qfdkM+VIgA==", "type": "package", "path": "webdav.client/2.9.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net45/WebDav.Client.dll", "lib/net45/WebDav.Client.pdb", "lib/net45/WebDav.Client.xml", "lib/netstandard1.1/WebDav.Client.dll", "lib/netstandard1.1/WebDav.Client.pdb", "lib/netstandard1.1/WebDav.Client.xml", "lib/netstandard2.0/WebDav.Client.dll", "lib/netstandard2.0/WebDav.Client.pdb", "lib/netstandard2.0/WebDav.Client.xml", "webdav.client.2.9.0.nupkg.sha512", "webdav.client.nuspec"]}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["MediaBrowser.Common >= 4.8.10", "MediaBrowser.Server.Core >= 4.8.10", "WebDav.Client >= 2.9.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.11", "restore": {"projectUniqueName": "D:\\Codes\\MeiamSubtitle\\Emby.MeiamSub.WebDav\\Emby.MeiamSub.Shooter.csproj", "projectName": "Emby.MeiamSub.Shooter", "projectPath": "D:\\Codes\\MeiamSubtitle\\Emby.MeiamSub.WebDav\\Emby.MeiamSub.Shooter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Codes\\MeiamSubtitle\\Emby.MeiamSub.WebDav\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"MediaBrowser.Common": {"target": "Package", "version": "[4.8.10, )"}, "MediaBrowser.Server.Core": {"target": "Package", "version": "[4.8.10, )"}, "WebDav.Client": {"suppressParent": "None", "target": "Package", "version": "[2.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.316\\RuntimeIdentifierGraph.json"}}}}