{"format": 1, "restore": {"D:\\Codes\\MeiamSubtitle\\Emby.MeiamSub.WebDav\\Emby.MeiamSub.Shooter.csproj": {}}, "projects": {"D:\\Codes\\MeiamSubtitle\\Emby.MeiamSub.WebDav\\Emby.MeiamSub.Shooter.csproj": {"version": "1.0.11", "restore": {"projectUniqueName": "D:\\Codes\\MeiamSubtitle\\Emby.MeiamSub.WebDav\\Emby.MeiamSub.Shooter.csproj", "projectName": "Emby.MeiamSub.Shooter", "projectPath": "D:\\Codes\\MeiamSubtitle\\Emby.MeiamSub.WebDav\\Emby.MeiamSub.Shooter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Codes\\MeiamSubtitle\\Emby.MeiamSub.WebDav\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"MediaBrowser.Common": {"target": "Package", "version": "[4.8.10, )"}, "MediaBrowser.Server.Core": {"target": "Package", "version": "[4.8.10, )"}, "WebDav.Client": {"suppressParent": "None", "target": "Package", "version": "[2.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.316\\RuntimeIdentifierGraph.json"}}}}}