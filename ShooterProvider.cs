using Emby.MeiamSub.Shooter.Model;
using MediaBrowser.Common.Net;
using MediaBrowser.Controller.Providers;
using MediaBrowser.Controller.Subtitles;
using MediaBrowser.Model.Logging;
using MediaBrowser.Model.Providers;
using MediaBrowser.Model.Serialization;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Net.Http;

namespace Emby.MeiamSub.Shooter
{
    /// <summary>
    /// 迅雷字幕组件
    /// </summary>
    public class ShooterProvider : ISubtitleProvider, IHasOrder
    {
        #region 变量声明
        public const string ASS = "ass";
        public const string SSA = "ssa";
        public const string SRT = "srt";

        protected readonly ILogger _logger;
        private readonly IJsonSerializer _jsonSerializer;
        private readonly IHttpClient _httpClient;

        public int Order => 1;
        public string Name => "MeiamSub.Shooter";

        /// <summary>
        /// 支持电影、剧集
        /// </summary>
        public IEnumerable<VideoContentType> SupportedMediaTypes => new List<VideoContentType>() { VideoContentType.Movie, VideoContentType.Episode };
        #endregion

        #region 构造函数
        public ShooterProvider(ILogManager logManager, IJsonSerializer jsonSerializer,IHttpClient httpClient)
        {
            _logger = logManager.GetLogger(GetType().Name);
            _jsonSerializer = jsonSerializer;
            _httpClient = httpClient;
            _logger.Info("{0} Init", new object[1] { Name });
        }
        #endregion

        #region 查询字幕

        /// <summary>
        /// 查询请求
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<IEnumerable<RemoteSubtitleInfo>> Search(SubtitleSearchRequest request, CancellationToken cancellationToken)
        {
            _logger.Info("{0} Search | SubtitleSearchRequest -> {1}", new object[2] { Name , _jsonSerializer.SerializeToString(request) });

            var subtitles = await SearchSubtitlesAsync(request);

            return subtitles;
        }

        /// <summary>
        /// 查询字幕
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private async Task<IEnumerable<RemoteSubtitleInfo>> SearchSubtitlesAsync(SubtitleSearchRequest request)
        {
            var remoteSubtitleInfos = new List<RemoteSubtitleInfo>();
            string tmdbId = request.ProviderIds != null && request.ProviderIds.ContainsKey("Tmdb") ? request.ProviderIds["Tmdb"] : null;
            if (string.IsNullOrEmpty(tmdbId))
            {
                _logger.Error("未提供 TMDB ID，无法匹配字幕。");
                return Array.Empty<RemoteSubtitleInfo>();
            }

            // 优化：先找到包含tmdbid的文件夹，再遍历该文件夹下字幕文件
            string webDavUrl = "https://vktdudkaokuymf.28.al/dav/";
            var handler = new HttpClientHandler();
            handler.Credentials = new NetworkCredential("7D9CC4C4E258C4EB23432D", "dbJZ$DLCXdakRiacSMBcP3");
            string tmdbTag = $"{{tmdbid={tmdbId}}}".ToLower();
            string targetFolder = null;
            using (var client = new HttpClient(handler))
            {
                // 先获取根目录下所有文件夹
                var propfindBody = "<?xml version=\"1.0\"?><d:propfind xmlns:d=\"DAV:\"><d:allprop/></d:propfind>";
                var requestMessage = new HttpRequestMessage(new HttpMethod("PROPFIND"), webDavUrl)
                {
                    Content = new StringContent(propfindBody, Encoding.UTF8, "application/xml")
                };
                requestMessage.Headers.Add("Depth", "1");
                var response = await client.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                {
                    _logger.Error($"WebDAV 查询失败: {response.StatusCode}");
                    return Array.Empty<RemoteSubtitleInfo>();
                }
                var xml = await response.Content.ReadAsStringAsync();
                var doc = new System.Xml.XmlDocument();
                doc.LoadXml(xml);
                var nsmgr = new System.Xml.XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("d", "DAV:");
                var nodes = doc.SelectNodes("//d:response", nsmgr);
                foreach (System.Xml.XmlNode node in nodes)
                {
                    var hrefNode = node.SelectSingleNode("d:href", nsmgr);
                    if (hrefNode != null)
                    {
                        var href = Uri.UnescapeDataString(hrefNode.InnerText);
                        // 只找文件夹
                        if (href.EndsWith("/") && href.ToLower().Contains(tmdbTag))
                        {
                            targetFolder = href;
                            break;
                        }
                    }
                }
                if (string.IsNullOrEmpty(targetFolder))
                {
                    _logger.Info($"未找到包含tmdbid={tmdbId}的文件夹");
                    return Array.Empty<RemoteSubtitleInfo>();
                }
                // 再获取该文件夹下所有字幕文件
                // 修正拼接方式，确保只有一个斜杠分隔
                // 修正：targetFolder为绝对路径（如/dav/xxx/），只拼接域名部分
                string folderUrl;
                if (targetFolder.StartsWith("/dav/"))
                {
                    folderUrl = "https://vktdudkaokuymf.28.al" + targetFolder;
                }
                else if (targetFolder.StartsWith("/"))
                {
                    folderUrl = webDavUrl.TrimEnd('/') + targetFolder;
                }
                else
                {
                    folderUrl = webDavUrl.TrimEnd('/') + "/" + targetFolder;
                }
                _logger.Info($"WebDAV 查询字幕文件夹URL: {folderUrl}");
                var folderRequest = new HttpRequestMessage(new HttpMethod("PROPFIND"), folderUrl)
                {
                    Content = new StringContent(propfindBody, Encoding.UTF8, "application/xml")
                };
                folderRequest.Headers.Add("Depth", "1");
                var folderResponse = await client.SendAsync(folderRequest);
                if (!folderResponse.IsSuccessStatusCode)
                {
                    _logger.Error($"WebDAV 查询字幕文件夹失败: {folderResponse.StatusCode}, URL: {folderUrl}");
                    return Array.Empty<RemoteSubtitleInfo>();
                }
                var folderXml = await folderResponse.Content.ReadAsStringAsync();
                var folderDoc = new System.Xml.XmlDocument();
                folderDoc.LoadXml(folderXml);
                var folderNodes = folderDoc.SelectNodes("//d:response", nsmgr);
                var subtitleFiles = new List<string>();
                foreach (System.Xml.XmlNode node in folderNodes)
                {
                    var hrefNode = node.SelectSingleNode("d:href", nsmgr);
                    if (hrefNode != null)
                    {
                        var href = Uri.UnescapeDataString(hrefNode.InnerText);
                        if (!href.EndsWith("/"))
                        {
                            // 只保留字幕文件
                            if (href.EndsWith(".srt", StringComparison.OrdinalIgnoreCase) ||
                                href.EndsWith(".ass", StringComparison.OrdinalIgnoreCase) ||
                                href.EndsWith(".ssa", StringComparison.OrdinalIgnoreCase))
                            {
                                subtitleFiles.Add(href);
                            }
                        }
                    }
                }
                _logger.Info($"WebDAV 匹配到字幕文件: {string.Join(", ", subtitleFiles)}");
                foreach (var file in subtitleFiles)
                {
                    string format = ExtractFormat(file);
                    string fileUrl = "https://vktdudkaokuymf.28.al" + file;
                    var idObj = new { fileUrl, format };
                    string idJson = _jsonSerializer.SerializeToString(idObj);
                    string idBase = Base64Encode(idJson);
                    _logger.Info($"WebDAV 字幕原始URL: {fileUrl}");
                    _logger.Info($"WebDAV 字幕ID JSON: {idJson}, Base64: {idBase}, 长度: {idBase.Length}");
                    remoteSubtitleInfos.Add(new RemoteSubtitleInfo()
                    {
                        Id = idBase,
                        Name = file,
                        Author = "WebDAV",
                        ProviderName = Name,
                        Format = format,
                        Comment = $"WebDAV字幕文件",
                        IsHashMatch = true
                    });
                }
            }
            _logger.Info($"WebDAV 查询 | TMDB ID={tmdbId} | 匹配字幕数: {remoteSubtitleInfos.Count}");
            return remoteSubtitleInfos;
        }
        #endregion

        #region 下载字幕
        /// <summary>
        /// 下载请求
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<SubtitleResponse> GetSubtitles(string id, CancellationToken cancellationToken)
        {
            _logger.Info("{0}  DownloadSub | Request -> {1}", new object[2] { Name, id });

            return await DownloadSubAsync(id);
        }

        /// <summary>
        /// 下载字幕
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        private async Task<SubtitleResponse> DownloadSubAsync(string info)
        {
            // info 是Base64编码的JSON对象（包含fileUrl和format）
            string idJson = Base64Decode(info.Replace(" ", "+"));
            _logger.Info($"WebDAV 下载字幕ID JSON: {idJson}");
            var idObj = _jsonSerializer.DeserializeFromString<Dictionary<string, string>>(idJson);
            string fileUrl = idObj["fileUrl"];
            string format = idObj["format"];
            _logger.Info($"WebDAV 下载字幕URL: {fileUrl}, 格式: {format}");
            using (var handler = new HttpClientHandler())
            {
                handler.Credentials = new NetworkCredential("7D9CC4C4E258C4EB23432D", "dbJZ$DLCXdakRiacSMBcP3");
                using (var client = new HttpClient(handler))
                {
                    var httpResponse = await client.GetAsync(fileUrl);
                    if (!httpResponse.IsSuccessStatusCode)
                    {
                        _logger.Error($"WebDAV 下载失败: {fileUrl}, 状态码: {httpResponse.StatusCode}");
                        return new SubtitleResponse();
                    }
                    var stream = await httpResponse.Content.ReadAsStreamAsync();
                    return new SubtitleResponse()
                    {
                        Language = null, // 不筛选语言
                        IsForced = false,
                        Format = format,
                        Stream = stream,
                    };
                }
            }
        }
        #endregion

        #region 内部方法
        /// <summary>
        /// 递归遍历WebDav所有文件，返回完整路径列表
        /// </summary>
        private async Task RecursivePropfind(HttpClient client, string url, List<string> allFiles, string relativePath = "/")
        {
            var propfindBody = "<?xml version=\"1.0\"?><d:propfind xmlns:d=\"DAV:\"><d:allprop/></d:propfind>";
            var requestMessage = new HttpRequestMessage(new HttpMethod("PROPFIND"), url + relativePath)
            {
                Content = new StringContent(propfindBody, Encoding.UTF8, "application/xml")
            };
            requestMessage.Headers.Add("Depth", "1");
            var response = await client.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                _logger.Error($"WebDAV 查询失败: {response.StatusCode}");
                return;
            }
            var xml = await response.Content.ReadAsStringAsync();
            try
            {
                var doc = new System.Xml.XmlDocument();
                doc.LoadXml(xml);
                var nsmgr = new System.Xml.XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("d", "DAV:");
                var nodes = doc.SelectNodes("//d:response", nsmgr);
                foreach (System.Xml.XmlNode node in nodes)
                {
                    var hrefNode = node.SelectSingleNode("d:href", nsmgr);
                    if (hrefNode != null)
                    {
                        var href = hrefNode.InnerText;
                        // 排除根目录本身
                        if (href == relativePath || href == url || href == url.TrimEnd('/')) continue;
                        // 判断是否为目录
                        var propNode = node.SelectSingleNode("d:prop", nsmgr);
                        var resTypeNode = propNode?.SelectSingleNode("d:resourcetype", nsmgr);
                        var collectionNode = resTypeNode?.SelectSingleNode("d:collection", nsmgr);
                        if (collectionNode != null)
                        {
                            // 是目录，递归
                            string subDir = href.TrimEnd('/');
                            _logger.Info($"WebDAV 目录: {Uri.UnescapeDataString(href)}");
                            await RecursivePropfind(client, url, allFiles, subDir + "/");
                        }
                        else
                        {
                            // 是文件，记录完整路径（相对根目录）
                            string filePath = href.StartsWith("/dav/") ? href.Substring("/dav/".Length) : href;
                            string decodedFilePath = Uri.UnescapeDataString(filePath);
                            _logger.Info($"WebDAV 文件: {decodedFilePath}");
                            // 只记录字幕文件
                            if (filePath.EndsWith(".srt", StringComparison.OrdinalIgnoreCase) ||
                                filePath.EndsWith(".ass", StringComparison.OrdinalIgnoreCase) ||
                                filePath.EndsWith(".ssa", StringComparison.OrdinalIgnoreCase))
                            {
                                allFiles.Add("/" + filePath);
                                _logger.Info($"WebDAV 识别为字幕文件: {decodedFilePath}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"WebDAV XML解析失败: {ex.Message}");
            }
        }

        /// <summary>
        /// Base64 加密
        /// </summary>
        /// <param name="plainText">明文</param>
        /// <returns></returns>
        public static string Base64Encode(string plainText)
        {
            var plainTextBytes = Encoding.UTF8.GetBytes(plainText);
            return Convert.ToBase64String(plainTextBytes);
        }
        /// <summary>
        /// Base64 解密
        /// </summary>
        /// <param name="base64EncodedData"></param>
        /// <returns></returns>
        public static string Base64Decode(string base64EncodedData)
        {
            var base64EncodedBytes = Convert.FromBase64String(base64EncodedData);
            return Encoding.UTF8.GetString(base64EncodedBytes);
        }

        /// <summary>
        /// 提取格式化字幕类型
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        protected string ExtractFormat(string text)
        {

            string result = null;

            if (text != null)
            {
                text = text.ToLower();
                if (text.Contains(ASS)) result = ASS;
                else if (text.Contains(SSA)) result = SSA;
                else if (text.Contains(SRT)) result = SRT;
                else result = null;
            }
            return result;
        }

        /// <summary>
        /// 获取文件 Hash (射手)
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static string ComputeFileHash(FileInfo fileInfo)
        {
            string ret = "";

            if (!fileInfo.Exists || fileInfo.Length < 8 * 1024)
            {
                return ret;
            }

            FileStream fs = new FileStream(fileInfo.FullName, FileMode.Open, FileAccess.Read);

            long[] offset = new long[4];
            offset[3] = fileInfo.Length - 8 * 1024;
            offset[2] = fileInfo.Length / 3;
            offset[1] = fileInfo.Length / 3 * 2;
            offset[0] = 4 * 1024;

            byte[] bBuf = new byte[1024 * 4];

            for (int i = 0; i < 4; ++i)
            {
                fs.Seek(offset[i], 0);
                fs.Read(bBuf, 0, 4 * 1024);

                MD5 md5Hash = MD5.Create();
                byte[] data = md5Hash.ComputeHash(bBuf);
                StringBuilder sBuilder = new StringBuilder();

                for (int j = 0; j < data.Length; j++)
                {
                    sBuilder.Append(data[j].ToString("x2"));
                }

                if (!string.IsNullOrEmpty(ret))
                {
                    ret += ";";
                }

                ret += sBuilder.ToString();
            }

            fs.Close();

            return ret;
        }
        #endregion
    }
}
